import { prisma } from "./prisma";

const USER_NAME_TOKEN = /\{\{\s*(user|username|user_name)\s*\}\}/gi;
const PERSONA_TOKEN = /\{\{\s*(persona|user_persona|user_description)\s*\}\}/gi;

const buildDefaultPersonaContent = (displayName: string) =>
  `I am ${displayName}, an engaged participant in this roleplay.`;

type EnsurePersonaOptions = {
  userId: string;
  userName?: string | null;
};

type PersonaRecord = {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type PersonaStrings = {
  name: string;
  description: string;
};

const normaliseDisplayName = (value?: string | null) => {
  const trimmed = value?.trim();
  return trimmed && trimmed.length > 0 ? trimmed : "User";
};

const reloadPersonas = async (userId: string) =>
  prisma.userPersona.findMany({
    where: { userId },
    orderBy: { createdAt: "asc" },
  });

export const ensureUserPersonas = async ({
  userId,
  userName,
}: EnsurePersonaOptions) => {
  const displayName = normaliseDisplayName(userName);

  let personas = await reloadPersonas(userId);

  let canonicalPersona = personas.find((persona) => persona.name === displayName) ?? null;

  if (!canonicalPersona) {
    const shouldBeDefault = personas.length === 0 || !personas.some((persona) => persona.isDefault);
    canonicalPersona = await prisma.userPersona.create({
      data: {
        userId,
        name: displayName,
        content: buildDefaultPersonaContent(displayName),
        isDefault: shouldBeDefault,
      },
    });
    personas = await reloadPersonas(userId);
  }

  let defaultPersona = personas.find((persona) => persona.isDefault) ?? canonicalPersona;

  if (!defaultPersona) {
    defaultPersona = canonicalPersona;
  }

  if (!defaultPersona.isDefault) {
    await prisma.userPersona.update({
      where: { id: defaultPersona.id },
      data: { isDefault: true },
    });
    defaultPersona = {
      ...defaultPersona,
      isDefault: true,
    };
  }

  await prisma.userPersona.updateMany({
    where: {
      userId,
      id: {
        not: defaultPersona.id,
      },
      isDefault: true,
    },
    data: { isDefault: false },
  });

  if (defaultPersona.id !== canonicalPersona.id) {
    await prisma.userPersona.update({
      where: { id: canonicalPersona.id },
      data: { name: displayName },
    });
    canonicalPersona = {
      ...canonicalPersona,
      name: displayName,
    };
  }

  personas = await reloadPersonas(userId);

  return {
    personas,
    canonicalPersona,
    defaultPersona: personas.find((persona) => persona.id === defaultPersona.id) ?? defaultPersona,
  };
};

export const getCurrentUserPersona = async ({
  userId,
  userName,
}: EnsurePersonaOptions): Promise<PersonaRecord> => {
  const { defaultPersona } = await ensureUserPersonas({ userId, userName });
  return defaultPersona;
};

export const replacePersonaPlaceholders = (
  value: string | null | undefined,
  persona: PersonaStrings,
) => {
  if (value == null) {
    return value ?? null;
  }

  let result = value;

  if (persona.name) {
    result = result.replace(USER_NAME_TOKEN, persona.name);
  }

  if (persona.description) {
    result = result.replace(PERSONA_TOKEN, persona.description);
  }

  return result;
};

export const applyPersonaToTextArray = (
  values: string[] | null | undefined,
  persona: PersonaStrings,
): string[] | null => {
  if (!Array.isArray(values)) {
    return values ?? null;
  }

  return values.map((value) => replacePersonaPlaceholders(value, persona) ?? "");
};

export const resolvePersonaStrings = (persona: { name: string; content: string }) => {
  const name = persona.name?.trim().length ? persona.name.trim() : "User";
  const description = persona.content?.trim().length
    ? persona.content.trim()
    : name;

  return { name, description } satisfies PersonaStrings;
};

type CharacterForPersona<T> = T & {
  description?: string | null;
  personality?: string | null;
  scenario?: string | null;
  creatorNotes?: string | null;
  firstMessage?: string | null;
  alternateGreetings?: string[];
  groupOnlyGreetings?: string[];
  postHistoryInstructions?: string | null;
  spec?: string | null;
  rawData?: unknown;
};

export const applyPersonaToCharacter = <T extends Record<string, unknown>>(
  character: CharacterForPersona<T>,
  persona: PersonaStrings,
) => {
  const result: Record<string, unknown> = {
    ...character,
  };

  if ("description" in character) {
    result.description = replacePersonaPlaceholders(character.description ?? null, persona);
  }
  if ("personality" in character) {
    result.personality = replacePersonaPlaceholders(character.personality ?? null, persona);
  }
  if ("scenario" in character) {
    result.scenario = replacePersonaPlaceholders(character.scenario ?? null, persona);
  }
  if ("creatorNotes" in character) {
    result.creatorNotes = replacePersonaPlaceholders(character.creatorNotes ?? null, persona);
  }
  if ("firstMessage" in character) {
    result.firstMessage = replacePersonaPlaceholders(character.firstMessage ?? null, persona);
  }
  if ("alternateGreetings" in character) {
    result.alternateGreetings = applyPersonaToTextArray(character.alternateGreetings, persona);
  }
  if ("groupOnlyGreetings" in character) {
    result.groupOnlyGreetings = applyPersonaToTextArray(character.groupOnlyGreetings, persona);
  }
  if ("postHistoryInstructions" in character) {
    result.postHistoryInstructions = replacePersonaPlaceholders(
      character.postHistoryInstructions ?? null,
      persona,
    );
  }
  if ("spec" in character) {
    result.spec = replacePersonaPlaceholders(character.spec ?? null, persona);
  }
  if ("rawData" in character && character.rawData && typeof character.rawData === "object") {
    const raw = JSON.parse(JSON.stringify(character.rawData)) as Record<string, unknown>;
    if (raw.data && typeof raw.data === "object") {
      for (const [key, value] of Object.entries(raw.data)) {
        if (typeof value === "string") {
          raw.data[key] = replacePersonaPlaceholders(value, persona);
        }
      }
    }
    result.rawData = raw;
  }

  if ("bookEntries" in character && Array.isArray(character.bookEntries)) {
    result.bookEntries = character.bookEntries.map((entry: Record<string, unknown>) => {
      if (!entry || typeof entry !== "object") {
        return entry;
      }

      const updatedEntry: Record<string, unknown> = { ...entry };
      if (typeof entry.content === "string") {
        updatedEntry.content = replacePersonaPlaceholders(entry.content, persona);
      }
      if (typeof entry.comment === "string") {
        updatedEntry.comment = replacePersonaPlaceholders(entry.comment, persona);
      }
      return updatedEntry;
    });
  }

  return result as CharacterForPersona<T>;
};
